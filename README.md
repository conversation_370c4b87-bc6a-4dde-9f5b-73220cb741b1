# 微健通小程序

专业皮肤病预约诊疗平台 - 小红书小程序版本

## 项目概述

微健通是一个专注于皮肤病预约诊疗的小红书小程序，为用户提供便捷的医生预约、优惠券管理等服务。

## 技术栈

- **平台**: 小红书小程序
- **开发框架**: 原生小程序开发
- **组件框架**: glass-easel
- **样式**: CSS3
- **数据管理**: 本地存储 + Mock服务

## 项目结构

```
├── app.js                 # 应用入口文件
├── app.json              # 应用配置文件
├── app.css               # 全局样式文件
├── sitemap.json          # 站点地图配置
├── config/               # 配置文件目录
│   ├── api.js           # API配置
│   └── constants.js     # 常量配置
├── utils/                # 工具类目录
│   ├── util.js          # 通用工具函数
│   ├── request.js       # 网络请求封装
│   ├── validator.js     # 表单验证工具
│   └── error-handler.js # 错误处理工具
├── services/             # 服务层目录
│   ├── mock.js          # Mock数据服务
│   ├── appointment.js   # 预约服务
│   ├── doctor.js        # 医生服务
│   └── coupon.js        # 优惠券服务
├── pages/                # 页面目录
│   ├── home/            # 首页
│   ├── profile/         # 个人中心
│   ├── appointments/    # 我的预约
│   ├── coupons/         # 优惠券
│   ├── appointment-detail/ # 预约详情
│   └── doctor-detail/   # 医生详情
└── images/               # 图片资源目录
```

## 主要功能

### 1. 首页预约
- 医生信息展示
- 预约表单填写
- 日期时间选择
- 优惠券展示
- 一键获取手机号

### 2. 我的预约
- 预约记录查看
- 预约状态跟踪
- 预约详情查看
- 取消预约功能
- 预约统计信息

### 3. 优惠券管理
- 优惠券列表展示
- 优惠券使用状态
- 优惠券领取功能
- 使用规则说明

### 4. 个人中心
- 用户信息管理
- 设置功能
- 帮助与反馈

## 代码优化亮点

### 1. 架构优化
- **分层架构**: 采用服务层、工具层、页面层的分层架构
- **模块化设计**: 将复杂功能拆分为独立的服务模块
- **配置集中管理**: 统一管理API配置和常量配置

### 2. 代码规范
- **小红书小程序规范**: 严格遵循小红书小程序开发规范
- **API适配**: 使用`xhs`替代`wx`API调用
- **文件命名**: 使用`.xhsml`、`.css`等小红书小程序文件扩展名

### 3. 业务逻辑优化
- **表单验证**: 统一的表单验证工具和规则
- **错误处理**: 全局错误处理机制
- **数据格式化**: 统一的数据格式化工具

### 4. 用户体验优化
- **加载状态**: 完善的加载状态提示
- **错误提示**: 友好的错误提示信息
- **交互反馈**: 及时的操作反馈

### 5. Mock数据服务
- **完整的Mock服务**: 模拟真实的后端API交互
- **数据持久化**: 使用本地存储保持数据状态
- **业务逻辑模拟**: 包含完整的业务逻辑处理

## 开发指南

### 环境要求
- 小红书开发者工具
- Node.js (可选，用于代码检查)

### 开发流程
1. 使用小红书开发者工具打开项目
2. 配置小程序AppID
3. 预览和调试功能
4. 真机测试

### 配置说明

#### API配置 (config/api.js)
```javascript
const API_CONFIG = {
  BASE_URL: 'https://api.weijiantong.com',
  TIMEOUT: 10000,
  ENDPOINTS: {
    LOGIN: '/api/user/login',
    SUBMIT_APPOINTMENT: '/api/appointment/submit',
    // ...更多接口
  }
}
```

#### 常量配置 (config/constants.js)
```javascript
const APPOINTMENT_TYPES = {
  VITILIGO: {
    key: 'vitiligo',
    label: '白癜风'
  },
  // ...更多类型
}
```

### 服务使用示例

#### 预约服务
```javascript
const appointmentService = require('../../services/appointment.js')

// 提交预约
const result = await appointmentService.submitAppointment(formData)

// 获取预约列表
const appointments = await appointmentService.getAppointments()
```

#### 医生服务
```javascript
const doctorService = require('../../services/doctor.js')

// 获取医生列表
const doctors = await doctorService.getDoctors()

// 获取推荐医生
const recommended = await doctorService.getRecommendedDoctors('vitiligo')
```

## 部署说明

1. **代码审核**: 确保代码符合小红书小程序规范
2. **功能测试**: 完成所有功能的测试
3. **提交审核**: 通过小红书开发者平台提交审核
4. **发布上线**: 审核通过后发布上线

## 注意事项

1. **API权限**: 确保小程序已申请相关API权限（如获取手机号）
2. **域名配置**: 在小红书开发者平台配置合法域名
3. **图片资源**: 确保所有图片资源路径正确
4. **兼容性**: 测试不同设备和系统版本的兼容性

## 后续优化建议

1. **接入真实API**: 替换Mock服务为真实的后端API
2. **数据缓存**: 实现更完善的数据缓存机制
3. **性能优化**: 进一步优化页面加载性能
4. **功能扩展**: 添加更多业务功能
5. **用户体验**: 持续优化用户交互体验

## 联系方式

如有问题或建议，请联系开发团队。

---

*本项目已完成代码重构和优化，业务逻辑更加清晰，代码结构更加合理，符合小红书小程序开发规范。*
