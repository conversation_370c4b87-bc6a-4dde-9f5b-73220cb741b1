// 测试页面 - 最简单的实现
console.log('=== 测试页面加载 ===')
console.log('typeof require:', typeof require)
console.log('typeof module:', typeof module)
console.log('typeof exports:', typeof exports)
console.log('typeof __webpack_require__:', typeof __webpack_require__)
console.log('typeof xhs:', typeof xhs)
console.log('typeof Page:', typeof Page)
console.log('====================')

Page({
  data: {
    message: '测试页面'
  },
  
  onLoad: function() {
    console.log('测试页面 onLoad')
    console.log('this.data:', this.data)
  },
  
  onReady: function() {
    console.log('测试页面 onReady')
  },
  
  onShow: function() {
    console.log('测试页面 onShow')
  }
})
