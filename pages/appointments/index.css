/* pages/appointments/index.css */
.appointments-page {
  min-height: 100vh;
  background: #f7f8fa;
}

/* 页面头部 */
.page-header {
  padding: 40rpx 0 20rpx;
  text-align: center;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

/* 预约列表 */
.appointment-list {
  display: flex;
  flex-direction: column;
  margin-bottom: 48rpx;
}

.appointment-item {
  background: #fff;
  padding: 48rpx;
  transition: transform 0.2s ease;
  margin-bottom:32rpx;
}

.appointment-item:active {
  transform: scale(0.98);
}

/* 预约头部 */
.appointment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.appointment-number {
  font-size: 26rpx;
  color: #666;
}

.appointment-status {
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: bold;
  color: #fff;
}

.appointment-status.status-pending {
  background: #ff9500;
}

.appointment-status.status-confirmed {
  background: #07c160;
}

.appointment-status.status-completed {
  background: #10aeff;
}

.appointment-status.status-cancelled {
  background: #fa5151;
}

/* 预约内容 */
.appointment-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20rpx;
}
.title {
  padding:32rpx 0;
  border-bottom:2rpx solid #f2f2f2;
}
.appointment-icon {
  width:72rpx;
  height:72rpx;
  border-radius: 50%;
  background-color:#de202c;
  text-align: center;
  padding-top:12rpx;
}
.appointment-icon image {
  width:48rpx;
  height:48rpx;
}
.appointment-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.info-row {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.info-label {
  font-size: 26rpx;
  color: #666;
  min-width: 80rpx;
}

.info-value {
  font-size:28rpx;
  font-weight:bold;
  color: #333;
}
.appointments-time {
  font-size: 20rpx;
  color: #999;
}

/* 用户信息样式 */
.user-info {
  display: flex;
  align-items: center;
  margin-top: 16rpx;
  padding: 8rpx 0;
}

.user-avatar {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  margin-right: 12rpx;
  border: 1rpx solid #eee;
}

.user-nickname {
  font-size: 24rpx;
  color: #666;
}
.create-time {
  font-size: 20rpx;
  color: #999;
  text-align: center;
  margin-bottom:48rpx;
}
.btn-status {
  padding:4px 16rpx;
  font-size:20rpx;
  color:#de202c;
  border:1px solid #de202c;
  border-radius: 50rpx;
}
/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  gap: 30rpx;
}

.empty-icon {
  font-size: 96rpx;
  margin-bottom: 64rpx;
  opacity: 0.3;
  width:256rpx;
  height:256rpx;
}
.empty-icon image {
  width:100%;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
}

.start-appointment-btn {
  width: 300rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background: #de202c;
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
  border: none;
}