<!--pages/coupons/index.wxml-->
<view class="coupons-page">
  <!-- 标签页 -->
  <view class="tabs">
    <view 
      class="tab {{activeTab === 'unused' ? 'active' : ''}}" 
      bindtap="switchTab"
      data-tab="unused"
    >
      未使用
    </view>
    <view 
      class="tab {{activeTab === 'used' ? 'active' : ''}}" 
      bindtap="switchTab"
      data-tab="used"
    >
      已使用
    </view>
  </view>

  <!-- 未使用优惠券 -->
  <view wx:if="{{activeTab === 'unused'}}" class="coupon-list">
    <view wx:if="{{unusedCoupons.length > 0}}">
      <view 
        wx:for="{{unusedCoupons}}" 
        wx:key="id"
        class="coupon-card"
      >
        <view class="coupon-content">
          <view class="coupon-header">
            <text class="coupon-title">{{item.title}}</text>
            <view class="coupon-price">
              <text class="price-value">{{item.price}}</text>
              <text class="price-unit">元</text>
            </view>
          </view>
          <text class="validity">有效期至：{{item.validUntil}}</text>
          
          <view class="coupon-info">
            <view class="usage-rules">
              <text class="rules-title">使用规则</text>
              <text 
                wx:for="{{item.rules}}" 
                wx:key="*this"
                wx:for-item="rule"
                class="rule-item"
              >
                {{rule}}
              </text>
            </view>
            <view class="coupon-footer">
              <button 
                class="use-btn" 
                type="primary" 
                size="mini"
                bindtap="useCoupon"
                data-id="{{item.id}}"
              >
                立即使用
              </button>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <view wx:else class="empty-state">
      <view class="empty-icon"><image src="../../images/que.png" mode="aspectFit"></image></view>
      <text class="empty-text">暂无未使用的优惠券</text>
    </view>
  </view>

  <!-- 已使用优惠券 -->
  <view wx:if="{{activeTab === 'used'}}" class="coupon-list">
    <view wx:if="{{usedCoupons.length > 0}}">
      <view 
        wx:for="{{usedCoupons}}" 
        wx:key="id"
        class="coupon-card disabled"
      >
        <view class="coupon-content">
          <view class="coupon-header">
            <text class="coupon-title">{{item.title}}</text>
            <view class="coupon-price">
              <text class="price-value">{{item.price}}</text>
              <text class="price-unit">元</text>
            </view>
          </view>        
          <text class="validity">有效期至：{{item.validUntil}}</text>
          <view class="coupon-info">
          <view class="usage-rules">
              <text class="rules-title">使用规则</text>
              <text 
                wx:for="{{item.rules}}" 
                wx:key="*this"
                wx:for-item="rule"
                class="rule-item"
              >
                {{rule}}
              </text>
            </view>
            <view class="used-tag">已使用</view>
          </view>
        </view>
      </view>
    </view>
    
    <view wx:else class="empty-state">
      <view class="empty-icon"><image src="../../images/que.png" mode="aspectFit"></image></view>
      <text class="empty-text">暂无已使用的优惠券</text>
    </view>
  </view>
</view>
