// pages/coupons/index.js
Page({
  data: {
    activeTab: 'unused',
    coupons: [
      {
        id: 1,
        title: '皮肤镜专业检测',
        price: 60,
        originalPrice: 120,
        validUntil: '2025-08-31',
        code: 'ILA1KJOKA5J1',
        rules: [
          '1.价值高达120元；',
          '2.本次援助最终解释权归合肥徽健通所有。'
        ],
        used: false,
        usedAt: null
      },
      {
        id: 2,
        title: '白癜风6维32项基础病因筛查',
        price: 0,
        originalPrice: 200,
        validUntil: '2025-08-31',
        code: 'WDF6W32JCBF2',
        rules: [
          '本次援助最终解释权归合肥徽健通所有。'
        ],
        used: false,
        usedAt: null
      },
      {
        id: 3,
        title: '美国进口 308 光斑',
        price: 0,
        originalPrice: 360,
        validUntil: '2025-08-31',
        code: 'USA308GB6X3',
        rules: [
          '1.价值高达360元；',
          '2.免费体验 6 个（限初诊）；',
          '3.本次援助最终解释权归合肥徽健通所有。'
        ],
        used: false,
        usedAt: null
      }
    ]
  },

  onLoad() {
    console.log('我的优惠券页面加载')
    this.updateCouponLists()
  },

  // 更新优惠券列表
  updateCouponLists() {
    const unusedCoupons = this.data.coupons.filter(coupon => !coupon.used)
    const usedCoupons = this.data.coupons.filter(coupon => coupon.used)
    
    this.setData({
      unusedCoupons,
      usedCoupons
    })
  },

  // 切换标签页
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab
    this.setData({
      activeTab: tab
    })
  },

  // 使用优惠券
  useCoupon(e) {
    const couponId = e.currentTarget.dataset.id
    console.log('使用优惠券:', couponId)

    // 跳转到优惠券使用页面
    xhs.navigateTo({
      url: `/pages/coupon-use/index?id=${couponId}`,
      success: () => {
        console.log('跳转到优惠券使用页面成功')
      },
      fail: (error) => {
        console.error('跳转失败:', error)
        xhs.showToast({
          title: '跳转失败',
          icon: 'none'
        })
      }
    })
  }
})
