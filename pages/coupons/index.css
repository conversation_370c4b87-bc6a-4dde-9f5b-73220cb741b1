/* pages/coupons/index.wxss */
.coupons-page {
  min-height: 100vh;
  background: #f7f8fa;
}

.tabs {
  display: flex;
  background: #fff;
  border-bottom: 1rpx solid #ebedf0;
}

.tab {
  flex: 1;
  text-align: center;
  padding: 32rpx 0;
  font-size: 32rpx;
  color: #646566;
  position: relative;
}

.tab.active {
  color: #de202c;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: #de202c;
  border-radius: 2rpx;
}

.coupon-list {
  padding: 48rpx;
  background:#fff;
}

.coupon-card.disabled {
  background: #fff;
}

.coupon-content {
  color: #333;
}

.coupon-card.disabled .coupon-content {
  color: #999;
}

.coupon-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.coupon-title {
  font-size: 32rpx;
  font-weight: bold;
  flex: 1;
  margin-right: 32rpx;
}

.coupon-price {
  text-align: right;
}

.price-value {
  font-size: 64rpx;
  font-weight: bold;
  line-height: 1;
  color:#de202c;
}

.price-unit {
  color:#de202c;
  font-size: 24rpx;
  margin-left: 8rpx;
}

.coupon-info {
  padding-bottom: 48rpx;
  margin-bottom:48rpx;
  display:flex;
  justify-content:space-between;
  border-bottom:2rpx solid #f2f2f2;
}

.validity {
  display: block;
  font-size: 20rpx;
  opacity: 0.9;
  padding-bottom:24rpx;
  color:#888;
}

.usage-rules .rules-title {
  display: block;
  font-size: 24rpx;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.rule-item {
  display: block;
  font-size: 20rpx;
  line-height: 1.4;
  margin-bottom: 8rpx;
  opacity: 0.8;
}

.coupon-footer {
  display: flex;
  justify-content: flex-end;
}

.use-btn {
  width: 130rpx;
  height: 48rpx;
  line-height: 48rpx;
  font-size: 20rpx;
  border-radius: 30rpx;
  background-color:#de202c;
}

.used-tag {
  padding: 12rpx 24rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  font-size: 24rpx;
  color: #999;
}

/* 优惠券边缘装饰 */
.coupon-card::before,
.coupon-card::after {
  content: '';
  position: absolute;
  width: 40rpx;
  height: 40rpx;
  background: #f7f8fa;
  border-radius: 50%;
  top: 50%;
  transform: translateY(-50%);
}

.coupon-card::before {
  left: -20rpx;
}

.coupon-card::after {
  right: -20rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.empty-icon {
  font-size: 96rpx;
  margin-bottom: 64rpx;
  opacity: 0.3;
  width:256rpx;
  height:256rpx;
}
.empty-icon image {
  width:100%;
}
.empty-text {
  color: #969799;
  font-size: 28rpx;
}
