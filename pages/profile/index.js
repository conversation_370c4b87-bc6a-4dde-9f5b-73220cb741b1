/**
 * 个人中心页面
 * 功能：用户信息展示、预约和优惠券管理入口
 */

// 移除 require 导入，使用本地数据

Page({
  data: {
    loading: true,
    userInfo: null,
    stats: {
      appointments: 0,
      coupons: 0,
      doctors: 0
    },
    recentAppointments: [],
    availableCoupons: []
  },

  /**
   * 页面加载
   */
  onLoad() {
    console.log('个人中心页面加载')
    this.initPageData()
  },

  /**
   * 页面显示
   */
  onShow() {
    // 每次显示时刷新数据
    this.refreshData()
  },

  onReady() {
    // 页面渲染完成时确保数据正确
    this.refreshData()
  },

  /**
   * 初始化页面数据
   */
  initPageData() {
    try {
      this.setData({ loading: true })

      // 获取用户信息
      this.getUserInfo()

      // 并行加载统计数据
      this.loadStats()

      // 加载最近的预约和优惠券
      this.loadRecentData()

    } catch (error) {
      console.error('初始化页面数据失败:', error)
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 刷新数据
   */
  refreshData() {
    try {
      this.loadStats()
      this.loadRecentData()
    } catch (error) {
      console.error('刷新数据失败:', error)
    }
  },

  /**
   * 获取用户信息
   */
  getUserInfo: function() {
    var that = this
    var app = getApp()

    // 尝试获取小红书用户信息
    app.getXhsUserInfo(function(userInfo) {
      that.setData({
        userInfo: {
          nickname: userInfo.nickName,
          avatar: userInfo.avatarUrl
        }
      })
    })
  },

  /**
   * 点击头像获取用户信息
   */
  onAvatarTap: function() {
    var that = this
    var app = getApp()

    xhs.showModal({
      title: '更新头像',
      content: '是否重新获取您的小红书头像和昵称？',
      success: function(res) {
        if (res.confirm) {
          app.getXhsUserInfo(function(userInfo) {
            that.setData({
              userInfo: {
                nickname: userInfo.nickName,
                avatar: userInfo.avatarUrl
              }
            })
            xhs.showToast({
              title: '更新成功',
              icon: 'success'
            })
          })
        }
      }
    })
  },

  /**
   * 加载统计数据
   */
  loadStats() {
    try {
      // 使用模拟数据
      this.setData({
        stats: {
          appointments: 2,
          coupons: 5,
          doctors: 5
        }
      })
    } catch (error) {
      console.error('加载统计数据失败:', error)
    }
  },

  /**
   * 加载最近的数据
   */
  loadRecentData: function() {
    try {
      // 使用模拟数据
      var recentAppointments = [
        {
          id: 1,
          appointmentType: '白癜风',
          selectedDate: '2025年01月15日',
          timeSlot: '上午',
          status: 'confirmed'
        }
      ]

      var availableCoupons = [
        {
          id: 1,
          title: '美国进口308光斑',
          description: '免费体验6个',
          tag: '（限初诊）',
          price: 0
        },
        {
          id: 2,
          title: '白癜风6维32项',
          description: '0元',
          tag: '基础病因筛查',
          price: 0
        }
      ]

      this.setData({
        recentAppointments,
        availableCoupons
      })
    } catch (error) {
      console.error('加载最近数据失败:', error)
    }
  },

  /**
   * 跳转到我的预约
   */
  goToAppointments() {
    xhs.navigateTo({
      url: '/pages/appointments/index'
    })
  },

  /**
   * 跳转到我的优惠券
   */
  goToCoupons() {
    xhs.navigateTo({
      url: '/pages/coupons/index'
    })
  },

  /**
   * 查看预约详情
   */
  viewAppointmentDetail: function(e) {
    var appointmentId = e.currentTarget.dataset.id
    xhs.navigateTo({
      url: '/pages/appointment-detail/index?id=' + appointmentId
    })
  },

  /**
   * 使用优惠券
   */
  useCoupon: function(e) {
    var couponId = e.currentTarget.dataset.id
    var that = this

    xhs.showLoading({ title: '使用中...' })

    // 模拟使用延迟
    setTimeout(function() {
      try {
        xhs.showToast({
          title: '使用成功',
          icon: 'success'
        })

        // 刷新数据
        that.refreshData()

      } catch (error) {
        console.error('使用优惠券失败:', error)
        xhs.showToast({
          title: '使用失败',
          icon: 'none'
        })
      } finally {
        xhs.hideLoading()
      }
    }, 800)
  },
  /**
   * 页面分享
   */
  onShareAppMessage() {
    return {
      title: '微健通 - 专业皮肤病预约诊疗平台',
      path: '/pages/home/<USER>'
    }
  }
})
