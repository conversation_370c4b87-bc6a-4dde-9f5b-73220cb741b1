/* pages/coupon-use/index.css */
.coupon-use-page {
  min-height: 100vh;
  background: #fff;
  padding: 48rpx;
}

/* 顶部标题区域 */
.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title-section {
  color:#666;
  display:flex;
  justify-content: space-between;
  align-items:center;
  padding-bottom:36rpx;
  border-bottom:2rpx solid #f2f2f2;
}

.coupon-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
}

.validity {
  font-size: 20rpx;
  color: #888;
}

/* 二维码区域 */

.qrcode-section {
  display: flex;
  justify-content: center;
  margin-bottom: 60rpx;
}

.qrcode-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.qrcode-canvas {
  border-radius: 12rpx;
  border: 2rpx solid #eee;
  background: white;
  display: block;
}

/* 中心LOGO样式 */
.qr-center-logo {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80rpx;
  height: 80rpx;
  z-index: 10;
}

.logo-image {
  width: 60rpx;
  height: 60rpx;
  border-radius: 8rpx;
}

/* 兑换码区域 */
.code-section {
  color:#666;
  text-align: center;
  margin-bottom:72rpx;
}

/* 使用说明 */
.instruction-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #666;
  margin-bottom: 24rpx;
}

.instruction-list {
  display: flex;
  flex-direction: column;
}

.instruction-item {
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 16rpx;
}