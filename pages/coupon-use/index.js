// pages/coupon-use/index.js
Page({
  data: {
    coupon: null
  },

  onLoad(options) {
    console.log('优惠券使用页面加载，参数:', options)
    
    const couponId = parseInt(options.id)
    if (couponId) {
      this.loadCouponData(couponId)
    } else {
      xhs.showToast({
        title: '参数错误',
        icon: 'none'
      })
      setTimeout(() => {
        xhs.navigateBack()
      }, 1500)
    }
  },

  onReady() {
    console.log('页面渲染完成，准备生成二维码')
    // 页面渲染完成后生成二维码
    if (this.data.coupon) {
      console.log('优惠券数据存在，开始生成二维码')
      setTimeout(() => {
        this.generateQRCode()
      }, 500) // 延迟500ms确保canvas已准备好
    } else {
      console.log('优惠券数据不存在')
    }
  },

  // 加载优惠券数据
  loadCouponData(couponId) {
    // 模拟优惠券数据
    const mockCoupons = [
      {
        id: 1,
        title: '皮肤镜专业检测',
        price: 60,
        originalPrice: 120,
        validUntil: '2025-08-31',
        code: 'ILA1KJOKA5J1',
        rules: [
          '1.价值高达120元；',
          '2.本次援助最终解释权归合肥徽健通所有。'
        ]
      },
      {
        id: 2,
        title: '白癜风6维32项基础病因筛查',
        price: 0,
        originalPrice: 200,
        validUntil: '2025-08-31',
        code: 'WDF6W32JCBF2',
        rules: [
          '本次援助最终解释权归合肥徽健通所有。'
        ]
      },
      {
        id: 3,
        title: '美国进口 308 光斑',
        price: 0,
        originalPrice: 360,
        validUntil: '2025-08-31',
        code: 'USA308GB6X3',
        rules: [
          '1.价值高达360元；',
          '2.免费体验 6 个（限初诊）；',
          '3.本次援助最终解释权归合肥徽健通所有。'
        ]
      }
    ]

    const coupon = mockCoupons.find(c => c.id === couponId)
    
    if (coupon) {
      this.setData({ coupon })
    } else {
      xhs.showToast({
        title: '优惠券不存在',
        icon: 'none'
      })
      setTimeout(() => {
        xhs.navigateBack()
      }, 1500)
    }
  },



  // 生成二维码（根据兑换码）
  generateQRCode() {
    console.log('开始生成二维码')

    // 先尝试简单版本
    this.drawSimpleQRCode()
  },

  // 绘制简单的二维码
  drawSimpleQRCode() {
    const ctx = xhs.createCanvasContext('qrcode', this)
    const couponCode = this.data.coupon.code

    console.log('绘制简单二维码，兑换码:', couponCode)

    // 设置canvas背景为白色
    ctx.setFillStyle('#FFFFFF')
    ctx.fillRect(0, 0, 400, 400)

    // 设置黑色填充
    ctx.setFillStyle('#000000')

    // 绘制边框
    ctx.fillRect(0, 0, 400, 20)   // 上边框
    ctx.fillRect(0, 0, 20, 400)   // 左边框
    ctx.fillRect(380, 0, 20, 400) // 右边框
    ctx.fillRect(0, 380, 400, 20) // 下边框

    // 绘制定位标记（左上角）
    ctx.fillRect(40, 40, 120, 120)
    ctx.setFillStyle('#FFFFFF')
    ctx.fillRect(60, 60, 80, 80)
    ctx.setFillStyle('#000000')
    ctx.fillRect(80, 80, 40, 40)

    // 绘制定位标记（右上角）
    ctx.fillRect(240, 40, 120, 120)
    ctx.setFillStyle('#FFFFFF')
    ctx.fillRect(260, 60, 80, 80)
    ctx.setFillStyle('#000000')
    ctx.fillRect(280, 80, 40, 40)

    // 绘制定位标记（左下角）
    ctx.fillRect(40, 240, 120, 120)
    ctx.setFillStyle('#FFFFFF')
    ctx.fillRect(60, 260, 80, 80)
    ctx.setFillStyle('#000000')
    ctx.fillRect(80, 280, 40, 40)

    // 根据兑换码生成一些随机点
    let codeHash = 0
    for (let i = 0; i < couponCode.length; i++) {
      codeHash += couponCode.charCodeAt(i)
    }

    // 在中间区域绘制一些点
    for (let i = 0; i < 50; i++) {
      const x = 180 + (codeHash * i * 7) % 40
      const y = 180 + (codeHash * i * 11) % 40
      ctx.fillRect(x, y, 8, 8)
    }

    ctx.draw(false, () => {
      console.log('简单二维码绘制完成')
    })
  },

  // 根据兑换码绘制二维码图案
  drawQRCodeFromCode(ctx, code) {
    const size = 20 // 20x20的网格
    const cellSize = 20 // 每个格子20px，总共400px

    ctx.setFillStyle('#000000')

    // 将兑换码转换为数字序列
    let codeHash = 0
    for (let i = 0; i < code.length; i++) {
      codeHash += code.charCodeAt(i)
    }

    console.log('兑换码哈希值:', codeHash)

    // 绘制定位标记（左上、右上、左下角）
    this.drawPositionMarker(ctx, 0, 0, cellSize)
    this.drawPositionMarker(ctx, (size - 7) * cellSize, 0, cellSize)
    this.drawPositionMarker(ctx, 0, (size - 7) * cellSize, cellSize)

    // 根据兑换码生成数据区域
    for (let i = 0; i < size; i++) {
      for (let j = 0; j < size; j++) {
        // 跳过定位标记区域和中心LOGO区域
        if (this.isPositionMarkerArea(i, j, size) || this.isCenterLogoArea(i, j, size)) {
          continue
        }

        // 根据兑换码和位置计算是否填充
        const seed = (codeHash + i * 31 + j * 17) % 100
        if (seed < 45) { // 约45%的填充率
          ctx.fillRect(j * cellSize, i * cellSize, cellSize, cellSize)
        }
      }
    }

    console.log('二维码图案绘制完成')
  },

  // 绘制定位标记
  drawPositionMarker(ctx, x, y, cellSize) {
    console.log(`绘制定位标记: x=${x}, y=${y}, cellSize=${cellSize}`)

    // 外框 7x7
    for (let i = 0; i < 7; i++) {
      for (let j = 0; j < 7; j++) {
        if (i === 0 || i === 6 || j === 0 || j === 6 ||
            (i >= 2 && i <= 4 && j >= 2 && j <= 4)) {
          ctx.fillRect(x + j * cellSize, y + i * cellSize, cellSize, cellSize)
        }
      }
    }
  },

  // 判断是否为定位标记区域
  isPositionMarkerArea(i, j, size) {
    return (i < 7 && j < 7) || // 左上
           (i < 7 && j >= size - 7) || // 右上
           (i >= size - 7 && j < 7) // 左下
  },

  // 判断是否为中心LOGO区域
  isCenterLogoArea(i, j, size) {
    const center = Math.floor(size / 2)
    const logoSize = 4 // LOGO区域大小
    return Math.abs(i - center) <= logoSize / 2 && Math.abs(j - center) <= logoSize / 2
  },

  // 复制兑换码
  copyCode() {
    const code = this.data.coupon.code
    xhs.setClipboardData({
      data: code,
      success: () => {
        xhs.showToast({
          title: '兑换码已复制',
          icon: 'success'
        })
      },
      fail: () => {
        xhs.showToast({
          title: '复制失败',
          icon: 'none'
        })
      }
    })
  },

  // 返回上一页
  goBack() {
    xhs.navigateBack()
  },

  // 确认使用优惠券
  confirmUse() {
    xhs.showModal({
      title: '确认使用',
      content: '确定要使用这张优惠券吗？使用后将无法撤销。',
      success: (res) => {
        if (res.confirm) {
          this.useCoupon()
        }
      }
    })
  },

  // 使用优惠券
  useCoupon() {
    xhs.showLoading({
      title: '处理中...'
    })

    // 模拟网络请求
    setTimeout(() => {
      xhs.hideLoading()
      
      xhs.showToast({
        title: '使用成功',
        icon: 'success'
      })

      // 延迟返回上一页
      setTimeout(() => {
        xhs.navigateBack()
      }, 1500)
    }, 2000)
  }
})
