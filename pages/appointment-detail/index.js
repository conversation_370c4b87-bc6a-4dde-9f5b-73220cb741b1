/**
 * 预约详情页面
 * 功能：显示预约的详细信息
 */

// 移除 require 导入，使用本地数据

Page({
  data: {
    loading: true,
    appointment: null,
    appointmentId: null
  },

  /**
   * 页面加载
   */
  onLoad: function(options) {
    var appointmentId = options.id
    if (!appointmentId) {
      xhs.showToast({
        title: '预约ID不能为空',
        icon: 'none'
      })
      setTimeout(function() {
        xhs.navigateBack()
      }, 1500)
      return
    }

    this.setData({ appointmentId: appointmentId })
    this.loadAppointmentDetail()
  },

  /**
   * 加载预约详情
   */
  loadAppointmentDetail: function() {
    try {
      this.setData({ loading: true })

      // 使用模拟数据
      var mockAppointments = [
        {
          id: 1,
          appointmentNumber: 'WJT20250102001',
          name: '张三',
          phone: '13800138000',
          age: '28',
          gender: '女',
          appointmentType: '白癜风',
          selectedDate: '2025年01月15日',
          timeSlot: '上午',
          status: 'confirmed',
          createdAt: '2025-01-02T10:30:00.000Z',
          doctorName: '张安平',
          doctorTitle: '皮肤科主任医师',
          hospital: '安徽医科大学第一附属医院'
        },
        {
          id: 2,
          appointmentNumber: 'WJT20250103002',
          name: '李四',
          phone: '13900139000',
          age: '35',
          gender: '男',
          appointmentType: '皮肤病',
          selectedDate: '2025年01月16日',
          timeSlot: '下午',
          status: 'cancelled',
          createdAt: '2025-01-03T09:15:00.000Z',
          doctorName: '李医生',
          doctorTitle: '皮肤科主任医师',
          hospital: '安徽医科大学第一附属医院'
        }
      ]

      var appointment = null
      for (var i = 0; i < mockAppointments.length; i++) {
        if (mockAppointments[i].id == this.data.appointmentId) {
          appointment = mockAppointments[i]
          break
        }
      }

      if (!appointment) {
        throw new Error('预约不存在')
      }

      // 格式化预约信息
      var formattedAppointment = this.formatAppointmentDetail(appointment)

      this.setData({ appointment: formattedAppointment })

    } catch (error) {
      console.error('加载预约详情失败:', error)
      xhs.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      })

      setTimeout(function() {
        xhs.navigateBack()
      }, 1500)
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 格式化预约详情
   */
  formatAppointmentDetail: function(appointment) {
    var statusMap = {
      confirmed: { label: '已预约', color: '#07c160' },
      completed: { label: '已完成', color: '#10aeff' },
      cancelled: { label: '已取消', color: '#fa5151' },
    }

    var status = statusMap[appointment.status] || statusMap.pending

    var result = {}
    for (var key in appointment) {
      result[key] = appointment[key]
    }
    result.statusText = status.label
    result.statusColor = status.color
    result.createTimeText = this.formatDateTime(appointment.createdAt)
    result.genderText = appointment.gender === 'male' ? '男' : '女'

    return result
  },

  /**
   * 格式化日期时间
   */
  formatDateTime: function(dateTimeString) {
    if (!dateTimeString) return ''

    var date = new Date(dateTimeString)
    var year = date.getFullYear()
    var month = (date.getMonth() + 1).toString()
    if (month.length === 1) month = '0' + month
    var day = date.getDate().toString()
    if (day.length === 1) day = '0' + day
    var hours = date.getHours().toString()
    if (hours.length === 1) hours = '0' + hours
    var minutes = date.getMinutes().toString()
    if (minutes.length === 1) minutes = '0' + minutes

    return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes
  },

  /**
   * 取消预约
   */
  cancelAppointment: function() {
    var that = this
    xhs.showModal({
      title: '确认取消',
      content: '确定要取消这个预约吗？取消后不可恢复。',
      success: function(res) {
        if (res.confirm) {
          xhs.showLoading({ title: '取消中...' })

          // 模拟取消延迟
          setTimeout(function() {
            try {
              xhs.showToast({
                title: '取消成功',
                icon: 'success'
              })

              // 刷新详情
              that.loadAppointmentDetail()

            } catch (error) {
              console.error('取消预约失败:', error)
              xhs.showToast({
                title: error.message || '取消失败',
                icon: 'none'
              })
            } finally {
              xhs.hideLoading()
            }
          }, 800)
        }
      },
      fail: function(error) {
        console.error('取消预约失败:', error)
        xhs.showToast({
          title: '取消失败',
          icon: 'none'
        })
      }
    })
  },
  
  /**
   * 页面分享
   */
  onShareAppMessage: function() {
    return {
      title: '微健通 - 预约详情',
      path: '/pages/appointment-detail/index?id=' + this.data.appointmentId
    }
  }
})
