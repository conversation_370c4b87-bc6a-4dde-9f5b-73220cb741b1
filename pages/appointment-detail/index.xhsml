<!--pages/appointment-detail/index.xhsml-->
<view class="appointment-detail-page">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 预约详情 -->
  <view wx:elif="{{appointment}}" class="detail-container">
    <!-- 状态卡片 -->
    <view class="status-card">
      <view class="status-header">
        <view class="status-badge status-{{appointment.status}}">
          {{appointment.statusText}}
        </view>
        <view class="appointment-number">
        <text class="create-time">{{appointment.createTimeText}}</text>
        {{appointment.appointmentNumber}}</view>
      </view>
      <view class="status-desc">
        <text wx:if="{{appointment.status === 'confirmed'}}">预约成功了！等待客服中心与您联系</text>
        <text wx:elif="{{appointment.status === 'completed'}}">您的预约已完成，感谢您的信任</text>
        <text wx:elif="{{appointment.status === 'cancelled'}}">您的预约已取消</text>
      </view>
      <view class="info-list">
        <view class="info-item">
          <text class="info-label">姓名</text>
          <text class="info-value">{{appointment.name}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">电话</text>
          <text class="info-value">{{appointment.phone}}</text>
        </view>
        <view class="info-item" wx:if="{{appointment.age}}">
          <text class="info-label">年龄</text>
          <text class="info-value">{{appointment.age}}岁</text>
        </view>
        <view class="info-item">
          <text class="info-label">性别</text>
          <text class="info-value">{{appointment.gender}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">预约类型</text>
          <text class="info-value">{{appointment.appointmentType}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">预约日期</text>
          <text class="info-value">{{appointment.selectedDate}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">时间段</text>
          <text class="info-value">{{appointment.timeSlot}}</text>
        </view>
      </view> 
    </view> 

    <!-- 温馨提示 -->
    <view class="tips-section">
      <view class="section-title">温馨提示</view>
      <view class="tips-content">
        <text class="tip-item">• 请提前15分钟到达医院</text>
        <text class="tip-item">• 请携带身份证和相关病历资料</text>
        <text class="tip-item">• 如需取消或改期，请提前24小时联系客服</text>
        <text class="tip-item">• 客服电话：400-123-4567</text>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <button 
        wx:if="{{appointment.status === 'confirmed'}}"
        class="action-btn cancel-btn" 
        bindtap="cancelAppointment"
      >
        取消预约
      </button>
    </view>
  </view> <!-- 关键：将detail-container的闭合标签移到这里，确保包含所有子内容 -->

  <!-- 错误状态 -->
  <view wx:else class="error-state">
    <view class="error-icon">❌</view>
    <text class="error-text">预约信息加载失败</text>
  </view>
</view>
