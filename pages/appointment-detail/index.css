/* pages/appointment-detail/index.css */
.appointment-detail-page {
  min-height: 100vh;
  background: #fff;
  padding: 48rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

/* 详情容器 */
.detail-container {
  padding-bottom: 40rpx;
}

/* 状态卡片 */

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin:32rpx 0;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 50rpx;
  font-size: 24rpx;
  font-weight: bold;
  color: #fff;
}

.status-badge.status-pending {
  border:2rpx solid #ff9500;
  color: #ff9500;
}

.status-badge.status-confirmed {
  border:2rpx solid #07c160;
  color: #07c160;
}

.status-badge.status-completed {
  border:2rpx solid #10aeff;
  color: #10aeff;
}

.status-badge.status-cancelled {
  border:2rpx solid #fa5151;
  color: #fa5151;
}

.appointment-number {
  font-size: 20rpx;
  color: #888;
}
.create-time {
  margin-right:24rpx;
}
.status-desc {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 96rpx;
}

/* 信息区域 */
.info-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  padding:0 24rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom:2rpx solid #f2f2f2;
}

.info-label {
  font-size: 24rpx;
  color: #888;
  min-width: 140rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  text-align: right;
}

/* 操作区域 */
.action-section {
  display:flex;
  margin:48rpx 0rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
}

.cancel-btn {
  background: #fa5151;
  color: #fff;
}

.contact-btn {
  background: #ff9500;
  color: #fff;
}

/* 温馨提示 */
.tips-section {
  margin-top:96rpx;
  padding:0 24rpx;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.tip-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

/* 错误状态 */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  gap: 20rpx;
}

.error-icon {
  font-size: 80rpx;
}

.error-text {
  font-size: 28rpx;
  color: #999;
}
