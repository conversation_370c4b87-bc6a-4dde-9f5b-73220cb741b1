// 简化版首页 - 纯 ES5 语法
console.log('=== 简化版首页加载 ===')
console.log('typeof require:', typeof require)
console.log('typeof module:', typeof module)
console.log('typeof exports:', typeof exports)
console.log('typeof __webpack_require__:', typeof __webpack_require__)
console.log('typeof xhs:', typeof xhs)
console.log('typeof Page:', typeof Page)
console.log('========================')

Page({
  data: {
    loading: true,
    message: '微健通首页',
    formData: {
      name: '',
      phone: '',
      age: '',
      gender: 'female',
      appointmentType: 'vitiligo',
      selectedDate: '',
      timeSlot: ''
    },
    coupons: [
      {
        id: 1,
        title: '美国进口308光斑',
        description: '免费体验6个',
        tag: '（限初诊）',
        received: true
      },
      {
        id: 2,
        title: '白癜风6维32项',
        description: '0元',
        tag: '基础病因筛查',
        received: false
      }
    ],
    doctors: [
      {
        id: 1,
        name: '张安平',
        title: '皮肤科主任医师',
        hospital: '安徽医科大学第一附属医院',
        avatar: '../../images/d-1.png'
      },
      {
        id: 2,
        name: '李医生',
        title: '皮肤科主任医师',
        hospital: '安徽医科大学第一附属医院',
        avatar: '../../images/d-2.png'
      }
    ]
  },

  onLoad: function() {
    console.log('简化版首页 onLoad')
    this.initPage()
  },

  onReady: function() {
    console.log('简化版首页 onReady')
  },

  onShow: function() {
    console.log('简化版首页 onShow')
    this.setData({ loading: false })
  },

  initPage: function() {
    console.log('初始化页面')
    this.initDatePicker()
  },

  initDatePicker: function() {
    var today = new Date()
    var year = today.getFullYear()
    var month = (today.getMonth() + 1).toString()
    if (month.length === 1) month = '0' + month
    var day = today.getDate().toString()
    if (day.length === 1) day = '0' + day
    
    var displayDate = year + '年' + month + '月' + day + '日'
    var currentHour = today.getHours()
    var defaultTimeSlot = currentHour < 11 ? '上午' : '下午'

    this.setData({
      'formData.selectedDate': displayDate,
      'formData.timeSlot': defaultTimeSlot
    })
  },

  onNameInput: function(e) {
    this.setData({ 'formData.name': e.detail.value })
  },

  onPhoneInput: function(e) {
    this.setData({ 'formData.phone': e.detail.value })
  },

  receiveCoupon: function(e) {
    var couponId = e.currentTarget.dataset.id
    var that = this
    
    console.log('领取优惠券:', couponId)
    
    xhs.showLoading({ title: '领取中...' })
    
    setTimeout(function() {
      xhs.hideLoading()
      xhs.showToast({
        title: '领取成功',
        icon: 'success'
      })
      console.log('优惠券领取成功')
    }, 800)
  },

  onSubmit: function() {
    console.log('提交表单')
    var formData = this.data.formData
    
    if (!formData.name) {
      xhs.showToast({ title: '请输入姓名', icon: 'none' })
      return
    }
    
    if (!formData.phone) {
      xhs.showToast({ title: '请输入电话', icon: 'none' })
      return
    }
    
    var that = this
    xhs.showLoading({ title: '提交中...' })
    
    setTimeout(function() {
      xhs.hideLoading()
      xhs.showToast({
        title: '提交成功',
        icon: 'success'
      })
      console.log('表单提交成功')
    }, 1200)
  }
})
