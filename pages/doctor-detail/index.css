/* pages/doctor-detail/index.css */
.doctor-detail-page {
  min-height: 100vh;
  background: #fff;
  padding: 20rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

/* 详情容器 */
.detail-container {
  padding:0rpx;
}

/* 医生卡片 */
.doctor-card {
  padding: 48rpx;
  margin-bottom: 20rpx;
}

.doctor-header {
  display: flex;
  gap: 24rpx;
}

.doctor-avatar {
  width: 144rpx;
  height: 144rpx;
  object-fit: cover; 
  object-position: top; 
  border-radius: 100rpx;
}

.doctor-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.doctor-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.doctor-title {
  font-size: 28rpx;
  color: #666;
}

.doctor-hospital {
  font-size: 26rpx;
  color: #999;
}

.doctor-experience {
  font-size: 24rpx;
  color: #07c160;
  background: rgba(7, 193, 96, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  align-self: flex-start;
  margin-top: 8rpx;
}

.doctor-stats {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 40rpx;
  padding-top: 30rpx;
  border-top: 2rpx solid #f0f0f0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.stat-number {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

.stat-divider {
  width: 2rpx;
  height: 40rpx;
  background: #f0f0f0;
}

/* 信息区域 */
.info-section {
  padding: 0rpx 48rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
  margin-top:48rpx;
}

/* 专科标签 */
.specialty-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.specialty-tag {
  padding: 12rpx 24rpx;
  background: rgba(222, 32, 44, 0.1);
  color: #de202c;
  font-size:24rpx;
  border-radius: 1020rpx;
  border: 2rpx solid rgba(222, 32, 44, 0.1);
}

/* 介绍内容 */
.introduction-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

/* 操作区域 */
.action-section {
  display: flex;
  gap: 20rpx;
  padding:48rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
}

.primary-btn {
  background: #de202c;
  color: #fff;
}

.secondary-btn {
  background: #fff;
  color: #de202c;
  border: 2rpx solid #de202c;
}

/* 功能区域 */

.function-item {
  display: flex;
  align-items: center;
  padding: 40rpx;
  gap: 24rpx;
}

.function-icon {
  font-size: 48rpx;
}

.function-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.function-title {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
}

.function-desc {
  font-size: 26rpx;
  color: #666;
}

.function-arrow {
  font-size: 32rpx;
  color: #ccc;
}

/* 温馨提示 */
.tips-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.tip-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

/* 错误状态 */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  gap: 20rpx;
}

.error-icon {
  font-size: 80rpx;
}

.error-text {
  font-size: 28rpx;
  color: #999;
}
