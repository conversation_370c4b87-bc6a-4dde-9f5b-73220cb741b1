<!--pages/doctor-detail/index.xhsml-->
<view class="doctor-detail-page">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 医生详情 -->
  <view wx:elif="{{doctor}}" class="detail-container">
    <!-- 医生基本信息 -->
    <view class="doctor-card">
      <view class="doctor-header">
        <image 
          class="doctor-avatar" 
          src="{{doctor.avatar}}" 
          mode="aspectFill"
          bindtap="previewAvatar"
        />
        <view class="doctor-info">
          <view class="doctor-name">{{doctor.name}}</view>
          <view class="doctor-title">{{doctor.title}}</view>
          <view class="doctor-hospital">{{doctor.hospital}}</view>
          <view class="doctor-experience">{{doctor.experience}}</view>
        </view>
      </view>
      
    </view>

    <!-- 专科信息 -->
    <view class="info-section">
      <view class="section-title">擅长专科</view>
      <view class="specialty-list">
        <text 
          wx:for="{{doctor.specialty}}" 
          wx:key="*this"
          class="specialty-tag"
        >
          {{item}}
        </text>
      </view>
     <view class="section-title">医生介绍</view>
      <view class="introduction-content">
        {{doctor.introduction}}
      </view>
    <view class="section-title">温馨提示</view>
      <view class="tips-content">
        <text class="tip-item">• 预约成功后，请按时就诊</text>
        <text class="tip-item">• 就诊时请携带身份证和相关病历资料</text>
        <text class="tip-item">• 如需取消或改期，请提前24小时联系客服</text>
        <text class="tip-item">• 客服电话：400-123-4567</text>
      </view>
    </view>
    <!-- 操作按钮 -->
    <view class="action-section">
      <button class="action-btn primary-btn" bindtap="appointmentDoctor">
        立即预约
      </button>
      <button class="action-btn secondary-btn" bindtap="onlineConsult">
        在线咨询
      </button>
    </view>
  </view>

  <!-- 错误状态 -->
  <view wx:else class="error-state">
    <view class="error-icon">❌</view>
    <text class="error-text">医生信息加载失败</text>
  </view>
</view>
