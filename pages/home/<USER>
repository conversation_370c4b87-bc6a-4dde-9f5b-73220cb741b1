<!--pages/home/<USER>
<view class="home-page">
  <!-- Banner图 -->
  <view class="banner"></view>

  <!-- 优惠券领取区域 -->
  <view class="coupon-section">
    <view class="coupon">
      <text class="coupon-title">皮肤三甲名医在线预约</text>
      <text class="coupon-subtitle">惠民援助现在就领</text>
      <text class="coupon-desc">ONLINE APPOINTMENT WITH TOP DERMATOLOGISTS</text>
    </view>




    <view wx:if="{{coupons && coupons.length > 0}}" class="coupons-container">
      <view
        wx:for="{{coupons}}"
        wx:key="id"
        class="coupon-ticket {{item.received ? 'received' : ''}}"
        bindtap="receiveCoupon"
        data-id="{{item.id}}"
      >
      <view class="coupon-inr">
        <view class="c-tit">{{item.title}}</view>
        <view class="c-desc">{{item.description}}</view>
        <view class="c-tag">{{item.tag}}</view>
      </view>
        <!-- 已领取标志 -->
    <view wx:if="{{item.received}}" class="received-mark">
        <image src="../../images/linq.png" class="received-icon" mode="aspectFit"></image>
    </view>
      </view>
    </view>

    <!-- 无数据提示 -->
    <view wx:else class="no-data-tip">
      <text>暂无优惠券数据</text>
    </view>
  </view>

  <!-- 医生介绍区域 -->
  <view class="doctor-section">
    <view wx:if="{{doctors && doctors.length > 0}}" class="doctors-container">
      <view
        wx:for="{{doctors}}"
        wx:key="id"
        class="doctor-item"
      >
      <image src="{{item.avatar}}" class="doctor-avatar" mode="heightFix"></image>
      <view class="doctor-info">
        <view class="doctor-name">
          {{item.name}}
          <text class="doctor-title">{{item.title}}</text>
        </view>
        <view class="doctor-hospital">{{item.hospital}}</view>
      </view>
      <button
        class="consult-btn"
        size="mini"
        type="primary"
        bindtap="consultDoctor"
        data-id="{{item.id}}"
      >
        有号
      </button>
      </view>
    </view>

    <!-- 无数据提示 -->
    <view wx:else class="no-data-tip">
      <text>暂无医生数据</text>
    </view>
  </view>

  <!-- 预约表单区域 -->
  <view class="appointment-section">
    <view class="form-title">提前填写-便捷更高效</view>
    
      <!-- 预约表单 -->
  <view class="form-container">
    <view class="form-cells">
      
      <!-- 姓名输入 -->
      <view class="form-cell">
        <view class="cell-label">姓名<text class="required">*</text></view>
        <view class="cell-body">
          <input class="cell-input" placeholder="请在此输入姓名" value="{{formData.name}}" bindinput="onNameInput" />
        </view>
      </view>
      
      <!-- 电话输入 -->
      <view class="form-cell">
        <view class="cell-label">电话<text class="required">*</text></view>
        <view class="cell-body">
          <input class="cell-input" placeholder="请在此输入电话" type="number" value="{{formData.phone}}" bindinput="onPhoneInput" />
        </view>
        <view class="cell-action">
          <button class="quick-btn" open-type="getPhoneNumber" bindgetphonenumber="onGetPhoneNumber" wx:if="{{!formData.phone}}">一键输入</button>
          <button class="quick-btn disabled" wx:else disabled>已获取</button>
        </view>
      </view>
      
      <!-- 年龄输入 -->
      <view class="form-cell">
        <view class="cell-label">年龄</view>
        <view class="cell-body">
          <input class="cell-input" placeholder="请在此输入年龄" type="number" value="{{formData.age}}" bindinput="onAgeInput" />
        </view>
      </view>
      
      <!-- 性别选择 -->
      <view class="form-cell">
        <view class="cell-label">性别</view>
        <view class="cell-body">
          <view class="radio-group">
            <view class="radio-item" bindtap="onGenderChange" data-value="male">
              <view class="radio-icon {{formData.gender === 'male' ? 'checked' : ''}}"></view>
              <text>男</text>
            </view>
            <view class="radio-item" bindtap="onGenderChange" data-value="female">
              <view class="radio-icon {{formData.gender === 'female' ? 'checked' : ''}}"></view>
              <text>女</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 预约类型选择 -->
      <view class="form-cell">
        <view class="cell-label">预约类型<text class="required">*</text></view>
        <view class="cell-body">
          <view class="radio-group">
            <view class="radio-item" bindtap="onAppointmentTypeChange" data-value="vitiligo">
              <view class="radio-icon {{formData.appointmentType === 'vitiligo' ? 'checked' : ''}}"></view>
              <text>白癜风</text>
            </view>
            <view class="radio-item" bindtap="onAppointmentTypeChange" data-value="skinDisease">
              <view class="radio-icon {{formData.appointmentType === 'skinDisease' ? 'checked' : ''}}"></view>
              <text>皮肤病</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 日期选择 -->
      <picker mode="date" value="{{currentDate}}" start="{{minDate}}" end="{{maxDate}}" bindchange="onDatePickerChange">
        <view class="form-cell cell-access">
          <view class="cell-label">选择日期<text class="required">*</text></view>
          <view class="cell-body">
            <view class="cell-text {{formData.selectedDate ? 'selected' : ''}}">{{formData.selectedDate || '选择日期'}}</view>
          </view>
          <view class="cell-arrow"></view>
        </view>
      </picker>
      
      <!-- 时间段选择 -->
      <view class="form-cell" wx:if="{{showTimeSlot}}">
        <view class="cell-label">时间段<text class="required">*</text></view>
        <view class="cell-body">
          <view class="radio-group">
            <view class="radio-item" bindtap="onTimeSlotChange" data-value="上午">
              <view class="radio-icon {{formData.timeSlot === '上午' ? 'checked' : ''}}"></view>
              <text>上午</text>
            </view>
            <view class="radio-item" bindtap="onTimeSlotChange" data-value="下午">
              <view class="radio-icon {{formData.timeSlot === '下午' ? 'checked' : ''}}"></view>
              <text>下午</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
   <view class="submit-container">
    <button
      class="submit-btn {{submitting ? 'disabled' : ''}}"
      type="primary"
      bindtap="onSubmit"
      disabled="{{submitting}}"
    >
      {{submitting ? '提交中...' : '提交预约'}}
    </button>
    <view class="wxts">
    温馨提示：为了能及时接收到预约结果，请接受挂号后的小程序授权提示</view>
  </view>
  </view>
</view>
