// 微健通小程序入口文件
// 调试信息：检查环境
console.log('=== 环境调试信息 ===')
console.log('typeof require:', typeof require)
console.log('typeof module:', typeof module)
console.log('typeof exports:', typeof exports)
console.log('typeof __webpack_require__:', typeof __webpack_require__)
console.log('typeof xhs:', typeof xhs)
console.log('===================')

App({
  onLaunch(options) {
    console.log('微健通启动')
    console.log('启动参数:', options)
    this.initApp()
  },

  onShow(options) {
    console.log('应用显示')
    console.log('显示参数:', options)
  },

  onHide() {
    console.log('应用隐藏')
  },

  onError(msg) {
    console.error('应用错误:', msg)
    console.error('错误堆栈:', new Error().stack)
  },

  initApp: function() {
    console.log('应用初始化完成')

    // 获取用户信息
    try {
      var userInfo = xhs.getStorageSync('userInfo')
      if (userInfo) {
        this.globalData.userInfo = userInfo
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
    }
  },

  getUserInfo: function() {
    return this.globalData.userInfo
  },

  setUserInfo: function(userInfo) {
    this.globalData.userInfo = userInfo
    try {
      xhs.setStorageSync('userInfo', userInfo)
    } catch (error) {
      console.error('保存用户信息失败:', error)
    }
  },

  /**
   * 获取小红书用户信息
   */
  getXhsUserInfo: function(callback) {
    var that = this

    // 如果已有用户信息，直接返回
    if (this.globalData.userInfo && this.globalData.userInfo.nickName) {
      console.log('使用缓存的用户信息:', this.globalData.userInfo)
      if (callback) callback(this.globalData.userInfo)
      return
    }

    console.log('开始获取小红书用户信息...')

    // 获取用户信息
    xhs.getUserProfile({
      desc: '用于完善用户资料',
      success: function(res) {
        console.log('获取用户信息成功:', res)
        var userInfo = {
          nickName: res.userInfo.nickName || '小红书用户',
          avatarUrl: res.userInfo.avatarUrl || '/images/default-avatar.png',
          gender: res.userInfo.gender || 0,
          city: res.userInfo.city || '',
          province: res.userInfo.province || '',
          country: res.userInfo.country || ''
        }

        that.setUserInfo(userInfo)
        console.log('用户信息已保存:', userInfo)
        if (callback) callback(userInfo)
      },
      fail: function(error) {
        console.error('获取用户信息失败:', error)

        // 尝试使用 getUserInfo 作为备选方案
        xhs.getUserInfo({
          success: function(res) {
            console.log('使用备选方案获取用户信息成功:', res)
            var userInfo = {
              nickName: res.userInfo.nickName || '小红书用户',
              avatarUrl: res.userInfo.avatarUrl || '/images/default-avatar.png',
              gender: res.userInfo.gender || 0,
              city: res.userInfo.city || '',
              province: res.userInfo.province || '',
              country: res.userInfo.country || ''
            }
            that.setUserInfo(userInfo)
            if (callback) callback(userInfo)
          },
          fail: function(error2) {
            console.error('备选方案也失败:', error2)
            // 使用默认用户信息
            var defaultUserInfo = {
              nickName: '小红书用户',
              avatarUrl: '/images/default-avatar.png',
              gender: 0
            }
            that.setUserInfo(defaultUserInfo)
            if (callback) callback(defaultUserInfo)
          }
        })
      }
    })
  },

  globalData: {
    userInfo: null,
    systemInfo: null,
    appVersion: '1.0.0'
  }
})
