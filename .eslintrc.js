/*
 * Eslint config file for 小红书小程序
 * Documentation: https://eslint.org/docs/user-guide/configuring/
 * Install the Eslint extension before using this feature.
 */
module.exports = {
  env: {
    es6: true,
    browser: true,
    // 移除 node: true，避免 Node.js 环境的 require 等全局变量
    node: false
  },
  ecmaFeatures: {
    // 禁用模块系统，小红书小程序不支持 ES6 模块
    modules: false
  },
  parserOptions: {
    ecmaVersion: 2018,
    // 改为 script 模式，避免模块化相关问题
    sourceType: 'script'
  },
  globals: {
    xhs: true,
    App: true,
    Page: true,
    getCurrentPages: true,
    getApp: true,
    Component: true,
    requirePlugin: true,
    requireMiniProgram: true,
    // 明确禁用可能导致问题的全局变量
    require: false,
    module: false,
    exports: false
  },
  // extends: 'eslint:recommended',
  rules: {
    // 禁用可能导致模块化代码的规则
    'no-undef': 'error'
  }
};