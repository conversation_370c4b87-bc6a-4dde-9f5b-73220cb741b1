# ES6 语法修复指南

## 问题原因
小红书小程序的 webpack 打包器会将 ES6+ 语法转换为使用 `require` 的代码，但运行时环境不支持 `require`，导致 `require is not defined` 错误。

## 解决方案
将所有 ES6+ 语法改为 ES5 兼容语法。

## 需要修复的语法

### 1. 方法定义
```javascript
// ES6 (有问题)
methodName() { }

// ES5 (正确)
methodName: function() { }
```

### 2. 变量声明
```javascript
// ES6 (有问题)
const variable = value
let variable = value

// ES5 (正确)
var variable = value
```

### 3. 模板字符串
```javascript
// ES6 (有问题)
`Hello ${name}`

// ES5 (正确)
'Hello ' + name
```

### 4. 箭头函数
```javascript
// ES6 (有问题)
(param) => { }

// ES5 (正确)
function(param) { }
```

### 5. 解构赋值
```javascript
// ES6 (有问题)
const [a, b] = array
const {name, age} = object

// ES5 (正确)
var a = array[0]
var b = array[1]
var name = object.name
var age = object.age
```

### 6. 扩展运算符
```javascript
// ES6 (有问题)
{ ...object, newProp: value }

// ES5 (正确)
var newObject = {}
for (var key in object) {
  newObject[key] = object[key]
}
newObject.newProp = value
```

### 7. Array.map 等方法
```javascript
// ES6 (有问题)
array.map(item => ({ ...item, newProp: value }))

// ES5 (正确)
var result = []
for (var i = 0; i < array.length; i++) {
  var item = array[i]
  var newItem = {}
  for (var key in item) {
    newItem[key] = item[key]
  }
  newItem.newProp = value
  result.push(newItem)
}
```

## 修复状态

### ✅ 已修复
- `pages/home/<USER>
- `pages/home-simple/index.js` - 简化版首页（测试用）
- `pages/test/index.js` - 测试页面

### 🔄 部分修复
- `pages/appointments/index.js` - 预约列表页（已修复主要方法）

### ❌ 待修复
- `pages/profile/index.js` - 个人中心页
- `pages/appointment-detail/index.js` - 预约详情页
- `pages/doctor-detail/index.js` - 医生详情页
- `pages/coupons/index.js` - 优惠券页
- `pages/coupon-use/index.js` - 优惠券使用页

## 建议
1. 首页已正常工作，可以先测试其他页面是否有问题
2. 只修复实际访问时出错的页面，避免过度修复
3. 保持原有功能不变，只改语法
